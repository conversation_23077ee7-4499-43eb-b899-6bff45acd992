package com.ehome.oc.controller.assets;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.common.utils.uuid.Seq;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 巡更管理控制器
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Controller
@RequestMapping("/oc/patrol")
public class PatrolController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(PatrolController.class);
    private String prefix = "oc/patrol";

    /**
     * 巡更配置管理页面
     */
    @GetMapping("/config")
    public String config() {
        return prefix + "/config";
    }

    /**
     * 巡更记录管理页面
     */
    @GetMapping("/record")
    public String record(@RequestParam(value = "location_name", required = false) String locationName, ModelMap mmap) {
        if (StringUtils.isNotEmpty(locationName)) {
            mmap.put("defaultLocationName", locationName);
        }
        return prefix + "/record";
    }

    /**
     * 获取巡更配置列表
     */
    @PostMapping("/config/list")
    @ResponseBody
    public TableDataInfo configList() {
        JSONObject params = getParams();
        String communityId = getSysUser().getCommunityId();

        EasySQL sql = new EasySQL();
        sql.append("FROM eh_patrol_config c");
        sql.append(communityId,"WHERE c.community_id = ?");

        // 添加搜索条件
        sql.appendLike(params.getString("location_name"), "AND c.location_name LIKE ?");
        sql.appendLike(params.getString("project_name"), "AND c.project_name LIKE ?");
        sql.append(params.getString("is_active"), "AND c.is_active = ?");
        sql.append("ORDER BY c.planned_time, c.create_time DESC");

        Page<Record> paginate = Db.paginate(
            params.getIntValue("pageNum"),
            params.getIntValue("pageSize"),
            "SELECT c.*, (SELECT COUNT(*) FROM eh_patrol_config_user u WHERE u.config_id = c.config_id) as user_count",
            sql.toFullSql()
        );

        return getDataTable(paginate);
    }

    /**
     * 获取巡更配置详情
     */
    @RequestMapping("/config/detail/{configId}")
    @ResponseBody
    public AjaxResult getConfigDetail(@PathVariable("configId") String configId) {
        String communityId = getSysUser().getCommunityId();
        
        // 获取配置信息
        Record config = Db.findFirst(
            "SELECT * FROM eh_patrol_config WHERE config_id = ? AND community_id = ?", 
            configId, communityId);
        
        if (config == null) {
            return AjaxResult.error("配置不存在");
        }
        
        // 获取巡更人员列表
        List<Record> users = Db.find(
            "SELECT * FROM eh_patrol_config_user WHERE config_id = ? ORDER BY create_time", 
            configId);
        
        config.set("users", recordToMap(users));
        return AjaxResult.success(config.toMap());
    }

    /**
     * 保存巡更配置
     */
    @Log(title = "巡更配置", businessType = BusinessType.INSERT)
    @PostMapping("/config/save")
    @ResponseBody
    public AjaxResult saveConfig() {
        try {
            JSONObject params = getParams();
            String communityId = getSysUser().getCommunityId();
            String currentTime = DateUtils.getTime();
            String currentUser = getSysUser().getLoginName();
            
            String configId = params.getString("config_id");
            boolean isUpdate = StringUtils.isNotEmpty(configId);

            if (!isUpdate) {
                configId = Seq.getId();
            }

            // 验证必填字段
            if (StringUtils.isEmpty(params.getString("location_name"))) {
                return AjaxResult.error("地点名称不能为空");
            }
            if (StringUtils.isEmpty(params.getString("project_name"))) {
                return AjaxResult.error("项目名称不能为空");
            }
            if (StringUtils.isEmpty(params.getString("planned_time"))) {
                return AjaxResult.error("计划时间不能为空");
            }
            
            // 保存配置信息
            if (isUpdate) {
                String updateSql = "UPDATE eh_patrol_config SET " +
                    "location_name = ?, project_name = ?, location_address = ?, planned_time = ?, " +
                    "longitude = ?, latitude = ?, location_range = ?, " +
                    "is_active = ?, patrol_users = ?, remark = ?, update_time = ?, updated_by = ? " +
                    "WHERE config_id = ? AND community_id = ?";

                Db.update(updateSql,
                    params.getString("location_name"),
                    params.getString("project_name"),
                    params.getString("location_address") != null ? params.getString("location_address") : "",
                    params.getString("planned_time"),
                    params.getDouble("longitude") != null ? params.getDouble("longitude") : 0,
                    params.getDouble("latitude") != null ? params.getDouble("latitude") : 0,
                    params.getInteger("location_range") != null ? params.getInteger("location_range") : 100,
                    params.getInteger("is_active") != null ? params.getInteger("is_active") : 1,
                    "", // patrol_users 字段，稍后会更新
                    params.getString("remark") != null ? params.getString("remark") : "",
                    currentTime,
                    currentUser,
                    configId,
                    communityId);
            } else {
                String insertSql = "INSERT INTO eh_patrol_config " +
                    "(config_id, community_id, location_name, project_name, location_address, planned_time, " +
                    "longitude, latitude, location_range, is_active, patrol_users, remark, create_time, created_by) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

                Db.update(insertSql,
                    configId,
                    communityId,
                    params.getString("location_name"),
                    params.getString("project_name"),
                    params.getString("location_address") != null ? params.getString("location_address") : "",
                    params.getString("planned_time"),
                    params.getDouble("longitude") != null ? params.getDouble("longitude") : 0,
                    params.getDouble("latitude") != null ? params.getDouble("latitude") : 0,
                    params.getInteger("location_range") != null ? params.getInteger("location_range") : 100,
                    params.getInteger("is_active") != null ? params.getInteger("is_active") : 1,
                    "", // patrol_users 字段，稍后会更新
                    params.getString("remark") != null ? params.getString("remark") : "",
                    currentTime,
                    currentUser);
            }
            
            // 更新巡更人员
            if (params.containsKey("users") && StringUtils.isNotEmpty(params.getString("users"))) {
                // 删除原有人员配置
                Db.update("DELETE FROM eh_patrol_config_user WHERE config_id = ?", configId);

                // 解析用户数据
                String usersStr = params.getString("users");
                JSONArray users;
                try {
                    users = JSONArray.parseArray(usersStr);
                } catch (Exception e) {
                    logger.error("解析用户数据失败: " + usersStr, e);
                    return AjaxResult.error("用户数据格式错误");
                }

                StringBuilder patrolUsersBuilder = new StringBuilder();

                for (int i = 0; i < users.size(); i++) {
                    JSONObject user = users.getJSONObject(i);
                    Db.update("INSERT INTO eh_patrol_config_user " +
                        "(config_id, user_id, user_name, user_phone, create_time) " +
                        "VALUES (?, ?, ?, ?, ?)",
                        configId,
                        user.getString("userId"),
                        user.getString("userName"),
                        user.getString("userPhone") != null ? user.getString("userPhone") : "",
                        currentTime);

                    // 构建巡更人员姓名字符串
                    if (patrolUsersBuilder.length() > 0) {
                        patrolUsersBuilder.append(",");
                    }
                    patrolUsersBuilder.append(user.getString("userName"));
                }

                // 更新 patrol_users 字段
                String patrolUsers = patrolUsersBuilder.toString();
                Db.update("UPDATE eh_patrol_config SET patrol_users = ? WHERE config_id = ?",
                    patrolUsers, configId);
            }
            
            return AjaxResult.success("保存成功");
            
        } catch (Exception e) {
            logger.error("保存巡更配置失败", e);
            return AjaxResult.error("保存失败：" + e.getMessage());
        }
    }

    /**
     * 删除巡更配置
     */
    @Log(title = "巡更配置", businessType = BusinessType.DELETE)
    @PostMapping("/config/delete")
    @ResponseBody
    public AjaxResult deleteConfig() {
        try {
            JSONObject params = getParams();
            String configId = params.getString("configId");
            String communityId = getSysUser().getCommunityId();
            
            if (StringUtils.isEmpty(configId)) {
                return AjaxResult.error("配置ID不能为空");
            }
            
            // 检查是否有关联的巡更记录
            int recordCount = Db.queryInt(
                "SELECT COUNT(*) FROM eh_patrol_record WHERE config_id = ?", configId);
            
            if (recordCount > 0) {
                return AjaxResult.error("该配置下存在巡更记录，无法删除");
            }
            
            // 删除人员配置
            Db.update("DELETE FROM eh_patrol_config_user WHERE config_id = ?", configId);
            
            // 删除配置
            int result = Db.update(
                "DELETE FROM eh_patrol_config WHERE config_id = ? AND community_id = ?", 
                configId, communityId);
            
            if (result > 0) {
                return AjaxResult.success("删除成功");
            } else {
                return AjaxResult.error("配置不存在或无权限删除");
            }
            
        } catch (Exception e) {
            logger.error("删除巡更配置失败", e);
            return AjaxResult.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 生成今日巡更任务
     */
    @Log(title = "生成巡更任务", businessType = BusinessType.INSERT)
    @PostMapping("/generateTodayTasks")
    @ResponseBody
    public AjaxResult generateTodayTasks() {
        try {
            String communityId = getSysUser().getCommunityId();
            String today = DateUtils.dateTimeNow("yyyy-MM-dd");
            String currentTime = DateUtils.getTime();

            // 检查今日是否已生成任务
            int existCount = Db.queryInt(
                "SELECT COUNT(*) FROM eh_patrol_record WHERE community_id = ? AND patrol_date = ?",
                communityId, today);

            if (existCount > 0) {
                return AjaxResult.error("今日任务已生成，请勿重复操作");
            }

            // 获取启用的巡更配置
            List<Record> configs = Db.find(
                "SELECT * FROM eh_patrol_config WHERE community_id = ? AND is_active = 1",
                communityId);

            int taskCount = 0;

            for (Record config : configs) {
                String configId = config.getStr("config_id");

                // 获取该配置的巡更人员
                List<Record> users = Db.find(
                    "SELECT * FROM eh_patrol_config_user WHERE config_id = ?", configId);

                // 为每个人员生成巡更任务
                for (Record user : users) {
                    String recordId = Seq.getId();

                    Db.update("INSERT INTO eh_patrol_record " +
                        "(record_id, config_id, community_id, location_name, location_address, " +
                        "planned_time, patrol_user_id, patrol_user_name, patrol_date, " +
                        "status, create_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 0, ?)",
                        recordId,
                        configId,
                        communityId,
                        config.getStr("location_name"),
                        config.getStr("location_address"),
                        config.getStr("planned_time"),
                        user.getStr("user_id"),
                        user.getStr("user_name"),
                        today,
                        currentTime);

                    taskCount++;
                }
            }

            return AjaxResult.success("成功生成 " + taskCount + " 个巡更任务");

        } catch (Exception e) {
            logger.error("生成今日巡更任务失败", e);
            return AjaxResult.error("生成任务失败：" + e.getMessage());
        }
    }

    /**
     * 获取巡更记录列表
     */
    @PostMapping("/record/list")
    @ResponseBody
    public TableDataInfo recordList() {
        JSONObject params = getParams();
        String communityId = getSysUser().getCommunityId();

        EasySQL sql = new EasySQL();
        sql.append("FROM eh_patrol_record r");
        sql.append("LEFT JOIN eh_patrol_config c ON r.config_id = c.config_id");
        sql.append("WHERE r.community_id = '" + communityId + "'");

        // 添加搜索条件
        // 处理日期范围查询 - 支持params[beginTime]和params[endTime]格式
        String beginTime = params.getString("params[beginTime]");
        String endTime = params.getString("params[endTime]");

        if (StringUtils.isNotEmpty(beginTime)) {
            sql.append(beginTime,"AND r.patrol_date >= ?");
        }
        if (StringUtils.isNotEmpty(endTime)) {
            sql.append(endTime,"AND r.patrol_date <= ?");
        }

        // 单个日期查询（兼容旧版本）
        sql.append(params.getString("patrol_date"), "AND r.patrol_date = ?");

        // 处理LIKE查询
        if (StringUtils.isNotEmpty(params.getString("location_name"))) {
            sql.appendLike(params.getString("location_name"),"AND r.location_name LIKE ?");
        }
        if (StringUtils.isNotEmpty(params.getString("patrol_user_name"))) {
            sql.appendLike(params.getString("patrol_user_name"),"AND r.patrol_user_name LIKE ?");
        }

        sql.append(params.getString("status"), "AND r.status = ?");
        sql.append("ORDER BY r.patrol_date DESC, r.planned_time, r.create_time DESC");

        Page<Record> paginate = Db.paginate(
            params.getIntValue("pageNum"),
            params.getIntValue("pageSize"),
            "SELECT r.*, c.location_range, c.longitude as target_lng, c.latitude as target_lat, c.project_name",
            sql.toFullSql()
        );

        return getDataTable(paginate);
    }

    /**
     * 获取巡更记录详情
     */
    @GetMapping("/record/detail/{recordId}")
    @ResponseBody
    public AjaxResult getRecordDetail(@PathVariable("recordId") String recordId) {
        String communityId = getSysUser().getCommunityId();

        Record record = Db.findFirst(
            "SELECT r.*, c.location_range, c.longitude as target_lng, c.latitude as target_lat, c.project_name " +
            "FROM eh_patrol_record r " +
            "LEFT JOIN eh_patrol_config c ON r.config_id = c.config_id " +
            "WHERE r.record_id = ? AND r.community_id = ?",
            recordId, communityId);

        if (record == null) {
            return AjaxResult.error("记录不存在");
        }

        return AjaxResult.success(record.toMap());
    }

    /**
     * 新增巡更配置页面
     */
    @GetMapping("/config/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 修改巡更配置页面
     */
    @GetMapping("/config/edit/{configId}")
    public String edit(@PathVariable("configId") String configId, ModelMap mmap) {
        String communityId = getSysUser().getCommunityId();

        Record config = Db.findFirst(
            "SELECT * FROM eh_patrol_config WHERE config_id = ? AND community_id = ?",
            configId, communityId);

        if (config != null) {
            mmap.put("patrolConfig", config.toMap());
        } else {
            // 如果没有找到配置，创建一个空的Map避免模板错误
            mmap.put("patrolConfig", new java.util.HashMap<String, Object>());
        }

        // 传递 configId 给模板
        mmap.put("configId", configId);

        return prefix + "/edit";
    }

    /**
     * 获取项目名称选项
     */
    @RequestMapping("/config/projectNameOptions")
    @ResponseBody
    public AjaxResult getProjectNameOptions() {
        // 常用项目名称列表
        String[] projectNames = {
            "安全巡查",
            "设施检查",
            "环境巡视",
            "消防检查",
            "设备维护",
            "绿化养护",
            "清洁检查",
            "夜间巡逻"
        };

        return AjaxResult.success(projectNames);
    }
}
