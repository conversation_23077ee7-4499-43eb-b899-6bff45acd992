package com.ehome.oc.controller.assets;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.common.utils.uuid.Seq;
import com.ehome.oc.service.IHouseInfoService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 房屋绑定关系管理控制器
 * 统一管理房屋与业主、车位、车辆的绑定关系
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/oc/binding")
public class HouseBindingController extends BaseController {

    @Autowired
    private IHouseInfoService houseInfoService;

    // ==================== 房屋绑定管理页面 ====================

    /**
     * 房屋绑定管理页面
     */
    @GetMapping("/house/manage/{houseId}")
    public String houseBindings(@PathVariable("houseId") String houseId, ModelMap mmap) {
        mmap.put("houseId", houseId);
        return "oc/house/house-bindings";
    }

    /**
     * 业主绑定管理页面
     */
    @GetMapping("/owner/manage/{ownerId}")
    public String ownerBindings(@PathVariable("ownerId") String ownerId, ModelMap mmap) {
        mmap.put("ownerId", ownerId);
        return "oc/user/house-bindings";
    }

    /**
     * 查看房屋绑定的住户页面
     */
    @GetMapping("/house-owner/list/{houseId}")
    public String houseOwners(@PathVariable("houseId") String houseId, ModelMap mmap) {
        mmap.put("houseId", houseId);
        return "oc/house/owners";
    }

    // ==================== 房屋绑定数据获取 ====================

    /**
     * 获取房屋绑定数据（从房屋角度）
     */
    @PostMapping("/house/data")
    @ResponseBody
    public AjaxResult getHouseBindingData() {
        JSONObject params = getParams();
        String houseId = params.getString("houseId");
        if (StringUtils.isEmpty(houseId)) {
            return AjaxResult.error("房屋ID不能为空");
        }

        try {
            Map<String, Object> result = new HashMap<>();

            // 获取房屋基本信息
            Record house = Db.findFirst(
                "SELECT h.*, b.name as building_name, u.name as unit_name " +
                "FROM eh_house_info h " +
                "LEFT JOIN eh_building b ON h.building_id = b.building_id " +
                "LEFT JOIN eh_unit u ON h.unit_id = u.unit_id " +
                "WHERE h.house_id = ?",
                houseId
            );

            if (house != null) {
                String houseAddress = (house.getStr("building_name") != null ? house.getStr("building_name") : "") +
                                    "/" + (house.getStr("unit_name") != null ? house.getStr("unit_name") : "") +
                                    "/" + (house.getStr("room") != null ? house.getStr("room") : "");
                result.put("houseAddress", houseAddress);
                result.put("houseArea", house.getBigDecimal("use_area"));
                result.put("houseFloor", house.getStr("floor"));

                // 房屋状态转换
                String houseStatus = house.getStr("house_status");
                String statusText = "未知";
                if ("2001".equals(houseStatus)) statusText = "已入住";
                else if ("2002".equals(houseStatus)) statusText = "未销售";
                else if ("2003".equals(houseStatus)) statusText = "已交房";
                else if ("2004".equals(houseStatus)) statusText = "未入住";
                else if ("2005".equals(houseStatus)) statusText = "已装修";
                else if ("2009".equals(houseStatus)) statusText = "装修中";
                result.put("houseStatus", statusText);
            }

            // 获取绑定的业主列表
            List<Record> owners = Db.find(
                "SELECT r.*, o.owner_name, o.mobile, o.id_card " +
                "FROM eh_house_owner_rel r " +
                "LEFT JOIN eh_owner o ON r.owner_id = o.owner_id " +
                "WHERE r.house_id = ? and r.check_status = 1 " +
                "ORDER BY r.is_default DESC, r.create_time DESC",
                houseId
            );
            result.put("owners", recordToMap(owners));

            // 获取绑定的车位列表
            List<Record> parkings = Db.find(
                "SELECT r.*, p.parking_no, p.parking_type " +
                "FROM eh_parking_house_rel r " +
                "LEFT JOIN eh_parking_space p ON r.parking_id = p.parking_id " +
                "WHERE r.house_id = ? " +
                "ORDER BY r.create_time DESC",
                houseId
            );
            result.put("parkings", recordToMap(parkings));

            // 获取绑定的车辆列表
            List<Record> vehicles = Db.find(
                "SELECT r.*, v.plate_no, v.vehicle_brand, v.vehicle_model " +
                "FROM eh_vehicle_house_rel r " +
                "LEFT JOIN eh_vehicle v ON r.vehicle_id = v.vehicle_id " +
                "WHERE r.house_id = ? " +
                "ORDER BY r.create_time DESC",
                houseId
            );
            result.put("vehicles", recordToMap(vehicles));

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取房屋绑定数据失败", e);
            return AjaxResult.error("获取数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取业主绑定数据（从业主角度）
     */
    @PostMapping("/owner/data")
    @ResponseBody
    public AjaxResult getOwnerBindingData() {
        JSONObject params = getParams();
        String ownerId = params.getString("ownerId");
        if (StringUtils.isEmpty(ownerId)) {
            return AjaxResult.error("业主ID不能为空");
        }

        try {
            // 获取业主基本信息
            Record owner = Db.findFirst("SELECT * FROM eh_owner WHERE owner_id = ?", ownerId);
            if (owner == null) {
                return AjaxResult.error("业主不存在");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("ownerName", owner.getStr("owner_name"));
            result.put("ownerMobile", owner.getStr("mobile"));
            result.put("houseInfo", owner.getStr("house_info"));

            // 获取绑定的房屋列表
            List<Record> houses = Db.find(
                "SELECT r.rel_id, r.house_id, r.rel_type, r.is_default, r.remark, h.combina_name, h.room, h.use_area " +
                "FROM eh_house_owner_rel r " +
                "LEFT JOIN eh_house_info h ON r.house_id = h.house_id " +
                "WHERE r.owner_id = ? and r.check_status = 1 " +
                "ORDER BY r.is_default DESC, r.create_time DESC",
                ownerId
            );
            result.put("houses", recordToMap(houses));

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取业主绑定数据失败", e);
            return AjaxResult.error("获取数据失败：" + e.getMessage());
        }
    }

    // ==================== 房屋-业主绑定管理 ====================

    /**
     * 获取可绑定的业主列表
     */
    @PostMapping("/house-owner/available")
    @ResponseBody
    public AjaxResult getAvailableOwners() {
        JSONObject params = getParams();
        String search = params.getString("search");
        String houseId = params.getString("houseId");
        int page = params.containsKey("page") ? params.getIntValue("page") : 1;
        int pageSize = 20;

        try {
            String sql = "SELECT owner_id as id, CONCAT(owner_name, ' (', mobile, ')') as text " +
                        "FROM eh_owner " +
                        "WHERE community_id = ? " +
                        "AND owner_id NOT IN (SELECT owner_id FROM eh_house_owner_rel WHERE house_id = ?) ";

            List<Object> sqlParams = new ArrayList<>();
            sqlParams.add(getSysUser().getCommunityId());
            sqlParams.add(houseId);

            if (StringUtils.isNotEmpty(search)) {
                sql += "AND (owner_name LIKE ? OR mobile LIKE ?) ";
                sqlParams.add("%" + search + "%");
                sqlParams.add("%" + search + "%");
            }

            sql += "ORDER BY owner_name LIMIT ?, ?";
            sqlParams.add((page - 1) * pageSize);
            sqlParams.add(pageSize);

            List<Record> list = Db.find(sql, sqlParams.toArray());

            Map<String, Object> result = new HashMap<>();
            result.put("data", recordToMap(list));
            result.put("total", list.size());

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取可绑定业主列表失败", e);
            return AjaxResult.error("获取数据失败：" + e.getMessage());
        }
    }

    /**
     * 添加业主绑定（从房屋角度）
     */
    @PostMapping("/house-owner/add")
    @ResponseBody
    @Log(title = "绑定业主", businessType = BusinessType.INSERT)
    public AjaxResult addOwnerBinding() {
        JSONObject params = getParams();
        String houseId = params.getString("houseId");
        String ownerId = params.getString("ownerId");
        Integer relType = params.getInteger("relType");

        if (StringUtils.isEmpty(houseId)) {
            return AjaxResult.error("房屋ID不能为空");
        }
        if (StringUtils.isEmpty(ownerId)) {
            return AjaxResult.error("业主ID不能为空");
        }
        if (relType == null) {
            relType = 1; // 默认为业主
        }

        try {
            // 检查是否已绑定
            Record existRel = Db.findFirst(
                "SELECT * FROM eh_house_owner_rel WHERE house_id = ? AND owner_id = ?",
                houseId, ownerId
            );

            if (existRel != null) {
                return AjaxResult.error("该业主已绑定到此房屋");
            }

            // 创建绑定关系
            Record rel = new Record();
            rel.set("rel_id", Seq.getId());
            rel.set("house_id", houseId);
            rel.set("owner_id", ownerId);
            rel.set("community_id", getSysUser().getCommunityId());
            rel.set("rel_type", relType);
            rel.set("is_default", 0);
            rel.set("check_status", 1); // 默认已审核
            setCreateAndUpdateInfo(rel);

            boolean success = Db.save("eh_house_owner_rel", "rel_id", rel);
            if (success) {
                // 更新房屋的绑定业主数量
                houseInfoService.updateHouseOwnerInfo(houseId);
                // 更新业主的房屋信息
                houseInfoService.updateOwnerHouseInfo(ownerId);
            }

            return toAjax(success);
        } catch (Exception e) {
            logger.error("绑定业主失败", e);
            return AjaxResult.error("绑定失败：" + e.getMessage());
        }
    }

    /**
     * 解除业主绑定（从房屋角度）
     */
    @PostMapping("/house-owner/remove")
    @ResponseBody
    @Log(title = "解绑业主", businessType = BusinessType.DELETE)
    public AjaxResult removeOwnerBinding() {
        JSONObject params = getParams();
        String relId = params.getString("relId");
        if (StringUtils.isEmpty(relId)) {
            return AjaxResult.error("关系ID不能为空");
        }

        try {
            Record rel = Db.findById("eh_house_owner_rel", "rel_id", relId);
            if (rel == null) {
                return AjaxResult.error("绑定关系不存在");
            }

            boolean success = Db.deleteById("eh_house_owner_rel", "rel_id", relId);
            if (success) {
                // 更新房屋的绑定业主数量
                String houseId = rel.getStr("house_id");
                String ownerId = rel.getStr("owner_id");
                houseInfoService.updateHouseOwnerInfo(houseId);
                houseInfoService.updateOwnerHouseInfo(ownerId);
            }

            return toAjax(success);
        } catch (Exception e) {
            logger.error("解绑业主失败", e);
            return AjaxResult.error("解绑失败：" + e.getMessage());
        }
    }

    /**
     * 查询房屋绑定的住户列表
     */
    @PostMapping("/house-owner/list")
    @ResponseBody
    public TableDataInfo ownerList() {
        JSONObject params = getParams();
        String houseId = params.getString("houseId");
        if (StringUtils.isEmpty(houseId)) {
            return getDataTable(new Page<>());
        }

        // 构建查询SQL
        EasySQL sql = new EasySQL();
        sql.append("SELECT r.*, o.owner_name, o.mobile, o.id_card, o.gender, o.remark, o.is_live,o.house_info FROM eh_house_owner_rel r");
        sql.append("LEFT JOIN eh_owner o ON r.owner_id = o.owner_id");
        sql.append(houseId,"WHERE r.house_id = ?");

        // 添加查询条件
        sql.appendLike(params.getString("owner_name"), "AND o.owner_name LIKE ?");
        sql.append(params.getString("rel_type"), "AND r.rel_type = ?");
        sql.append(params.getString("check_status"), "AND r.check_status = ?");

        // 默认按是否默认和创建时间排序
        sql.append("ORDER BY r.is_default DESC, r.create_time DESC");

        // 执行查询
        List<Record> list = Db.find(sql.getSQL(),sql.getParams());

        return getDataList(list);
    }

    // ==================== 业主-房屋绑定管理（从业主角度） ====================

    /**
     * 获取业主关联的房屋列表
     */
    @PostMapping("/owner-house/list")
    @ResponseBody
    public TableDataInfo ownerHouseList() {
        JSONObject params = getParams();
        String ownerId = params.getString("ownerId");
        if (StringUtils.isEmpty(ownerId)) {
            return getDataTable(new Page<>());
        }

        List<Record> houses = Db.find(
            "SELECT r.*, h.building_name, h.unit_name, h.room, h.total_area, h.house_status " +
            "FROM eh_house_owner_rel r " +
            "LEFT JOIN eh_house_info h ON r.house_id = h.house_id " +
            "WHERE r.owner_id = ? " +
            "ORDER BY r.is_default DESC, r.create_time DESC",
            ownerId
        );

        return getDataList(houses);
    }

    /**
     * 获取可选房屋列表（从业主角度）
     */
    @PostMapping("/owner-house/available")
    @ResponseBody
    public AjaxResult getAvailableHouses() {
        JSONObject params = getParams();
        String search = params.getString("search");
        String ownerId = params.getString("ownerId");
        Integer page = params.getInteger("page");

        if (page == null) page = 1;
        int pageSize = 20; // 每页20条数据

        try {
            EasySQL sql = new EasySQL();
            sql.append("FROM eh_house_info WHERE 1=1");
            sql.append(getSysUser().getCommunityId(), "AND community_id = ?");
            sql.append("AND check_status = 1"); // 只显示已审核的房屋

            // 排除已绑定给该业主的房屋
            if (StringUtils.isNotEmpty(ownerId)) {
                sql.append("AND house_id NOT IN (SELECT house_id FROM eh_house_owner_rel WHERE owner_id = '" + ownerId + "')");
            }

            // 搜索条件 - 如果有搜索词则添加搜索条件
            if (StringUtils.isNotEmpty(search)) {
                sql.append("AND (combina_name LIKE '%" + search + "%' OR room LIKE '%" + search + "%')");
            }

            sql.append("ORDER BY combina_name, room");

            // 使用分页查询
            Page<Record> pageResult = Db.paginate(page, pageSize,
                "SELECT house_id as id, CONCAT(combina_name, '/', room, ' (', use_area, '㎡)') as text",
                sql.toFullSql());

            return AjaxResult.success(recordToMap(pageResult.getList()));
        } catch (Exception e) {
            logger.error("获取可选房屋列表失败", e);
            return AjaxResult.error("获取房屋列表失败");
        }
    }

    /**
     * 添加房屋绑定（从业主角度）
     */
    @PostMapping("/owner-house/add")
    @ResponseBody
    @Log(title = "绑定房屋", businessType = BusinessType.INSERT)
    public AjaxResult addHouseBinding() {
        JSONObject params = getParams();
        String ownerId = params.getString("ownerId");
        String houseId = params.getString("houseId");
        Integer relType = params.getInteger("relType");

        if (StringUtils.isEmpty(ownerId)) {
            return AjaxResult.error("业主ID不能为空");
        }
        if (StringUtils.isEmpty(houseId)) {
            return AjaxResult.error("房屋ID不能为空");
        }
        if (relType == null) {
            relType = 1; // 默认为业主
        }

        try {
            // 检查是否已绑定
            Record existRel = Db.findFirst(
                "SELECT * FROM eh_house_owner_rel WHERE house_id = ? AND owner_id = ?",
                houseId, ownerId
            );

            if (existRel != null) {
                return AjaxResult.error("该业主已绑定到此房屋");
            }

            // 创建绑定关系
            Record rel = new Record();
            rel.set("rel_id", Seq.getId());
            rel.set("house_id", houseId);
            rel.set("owner_id", ownerId);
            rel.set("community_id", getSysUser().getCommunityId());
            rel.set("rel_type", relType);
            rel.set("is_default", 0);
            rel.set("check_status", 1); // 默认已审核
            rel.set("create_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
            rel.set("create_by", getSysUser().getLoginName());

            boolean success = Db.save("eh_house_owner_rel", "rel_id", rel);
            if (success) {
                // 更新房屋的绑定业主数量
                houseInfoService.updateHouseOwnerInfo(houseId);
                // 更新业主的房屋信息
                houseInfoService.updateOwnerHouseInfo(ownerId);
            }

            return toAjax(success);
        } catch (Exception e) {
            logger.error("绑定房屋失败", e);
            return AjaxResult.error("绑定失败：" + e.getMessage());
        }
    }

    /**
     * 移除房屋绑定（从业主角度）
     */
    @PostMapping("/owner-house/remove")
    @ResponseBody
    @Log(title = "解绑房屋", businessType = BusinessType.DELETE)
    public AjaxResult removeHouseBinding() {
        JSONObject params = getParams();
        String relId = params.getString("relId");

        if (StringUtils.isEmpty(relId)) {
            return AjaxResult.error("关系ID不能为空");
        }

        try {
            // 获取绑定关系信息
            Record rel = Db.findFirst("SELECT * FROM eh_house_owner_rel WHERE rel_id = ?", relId);
            if (rel == null) {
                return AjaxResult.error("绑定关系不存在");
            }

            String houseId = rel.getStr("house_id");
            String ownerId = rel.getStr("owner_id");

            // 删除绑定关系
            boolean success = Db.deleteById("eh_house_owner_rel", "rel_id", relId);
            if (success) {
                houseInfoService.updateHouseOwnerInfo(houseId);
                // 更新业主的房屋信息
                houseInfoService.updateOwnerHouseInfo(ownerId);
            }

            return toAjax(success);
        } catch (Exception e) {
            logger.error("解绑房屋失败", e);
            return AjaxResult.error("解绑失败：" + e.getMessage());
        }
    }

    // ==================== 房屋-车位绑定管理 ====================

    /**
     * 获取可绑定的车位列表
     */
    @PostMapping("/house-parking/available")
    @ResponseBody
    public AjaxResult getAvailableParkings() {
        JSONObject params = getParams();
        String search = params.getString("search");
        String houseId = params.getString("houseId");
        int page = params.containsKey("page") ? params.getIntValue("page") : 1;
        int pageSize = 20;

        try {
            String sql = "SELECT parking_id as id, parking_no as text " +
                        "FROM eh_parking_space " +
                        "WHERE community_id = ? " +
                        "AND parking_id NOT IN (SELECT parking_id FROM eh_parking_house_rel WHERE house_id = ?) ";

            List<Object> sqlParams = new ArrayList<>();
            sqlParams.add(getSysUser().getCommunityId());
            sqlParams.add(houseId);

            if (StringUtils.isNotEmpty(search)) {
                sql += "AND parking_no LIKE ? ";
                sqlParams.add("%" + search + "%");
            }

            sql += "ORDER BY parking_no LIMIT ?, ?";
            sqlParams.add((page - 1) * pageSize);
            sqlParams.add(pageSize);

            List<Record> list = Db.find(sql, sqlParams.toArray());

            Map<String, Object> result = new HashMap<>();
            result.put("data", recordToMap(list));
            result.put("total", list.size());

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取可绑定车位列表失败", e);
            return AjaxResult.error("获取数据失败：" + e.getMessage());
        }
    }

    /**
     * 添加车位绑定
     */
    @PostMapping("/house-parking/add")
    @ResponseBody
    @Log(title = "绑定车位", businessType = BusinessType.INSERT)
    public AjaxResult addParkingBinding() {
        JSONObject params = getParams();
        String houseId = params.getString("houseId");
        String parkingId = params.getString("parkingId");

        if (StringUtils.isEmpty(houseId)) {
            return AjaxResult.error("房屋ID不能为空");
        }
        if (StringUtils.isEmpty(parkingId)) {
            return AjaxResult.error("车位ID不能为空");
        }

        try {
            // 检查是否已绑定
            Record existRel = Db.findFirst(
                "SELECT * FROM eh_parking_house_rel WHERE house_id = ? AND parking_id = ?",
                houseId, parkingId
            );

            if (existRel != null) {
                return AjaxResult.error("该车位已绑定到此房屋");
            }

            // 创建绑定关系
            Record rel = new Record();
            rel.set("rel_id", Seq.getId());
            rel.set("community_id", getSysUser().getCommunityId());
            rel.set("house_id", houseId);
            rel.set("parking_id", parkingId);
            rel.set("is_default", 0);
            rel.set("check_status", "1"); // 默认已审核
            setCreateAndUpdateInfo(rel);

            boolean success = Db.save("eh_parking_house_rel", "rel_id", rel);
            if (success) {
                // 同步更新车位表中的房屋信息
                updateParkingHouseInfo(parkingId, houseId);
            }
            return toAjax(success);
        } catch (Exception e) {
            logger.error("绑定车位失败", e);
            return AjaxResult.error("绑定失败：" + e.getMessage());
        }
    }

    /**
     * 解除车位绑定
     */
    @PostMapping("/house-parking/remove")
    @ResponseBody
    @Log(title = "解绑车位", businessType = BusinessType.DELETE)
    public AjaxResult removeParkingBinding() {
        JSONObject params = getParams();
        String relId = params.getString("relId");
        if (StringUtils.isEmpty(relId)) {
            return AjaxResult.error("关系ID不能为空");
        }

        try {
            Record rel = Db.findById("eh_parking_house_rel", "rel_id", relId);
            if (rel == null) {
                return AjaxResult.error("绑定关系不存在");
            }

            String parkingId = rel.getStr("parking_id");
            boolean success = Db.deleteById("eh_parking_house_rel", "rel_id", relId);
            if (success) {
                // 清空车位表中的房屋信息
                clearParkingHouseInfo(parkingId);
            }
            return toAjax(success);
        } catch (Exception e) {
            logger.error("解绑车位失败", e);
            return AjaxResult.error("解绑失败：" + e.getMessage());
        }
    }

    /**
     * 查询房屋绑定的车位列表
     */
    @RequestMapping("/house-parking/list")
    @ResponseBody
    public TableDataInfo parkingList() {
        JSONObject params = getParams();
        String houseId = params.getString("houseId");
        if (StringUtils.isEmpty(houseId)) {
            return getDataTable(new Page<>());
        }
        // 查询车位绑定关系及车位信息
        String sql = "SELECT r.rel_id, r.house_id, r.parking_id, r.check_status, r.remark, r.create_by, r.create_time, r.update_by, r.update_time, " +
                     "p.parking_no, p.parking_type, p.parking_status, p.parking_area, p.plate_no " +
                     "FROM eh_parking_house_rel r " +
                     "LEFT JOIN eh_parking_space p ON r.parking_id = p.parking_id " +
                     "WHERE r.house_id = ? " +
                     "ORDER BY r.create_time DESC";
        List<Record> list = Db.find(sql, houseId);
        return getDataList(list);
    }

    // ==================== 工具方法 ====================

    /**
     * 更新车位表中的房屋信息
     */
    private void updateParkingHouseInfo(String parkingId, String houseId) {
        try {
            // 获取房屋信息
            Record house = Db.findFirst("SELECT combina_name,room FROM eh_house_info WHERE house_id = ?", houseId);
            if (house != null) {
                String combinaName = house.getStr("combina_name");
                String houseName = house.getStr("room");
                // 更新车位表中的房屋信息
                Db.update("UPDATE eh_parking_space SET house_id = ?, house_name = ? WHERE parking_id = ?",
                    houseId, combinaName+"/"+houseName, parkingId);
            }
        } catch (Exception e) {
            logger.error("更新车位房屋信息失败", e);
        }
    }

    /**
     * 清空车位表中的房屋信息
     */
    private void clearParkingHouseInfo(String parkingId) {
        try {
            // 清空车位表中的房屋信息
            Db.update("UPDATE eh_parking_space SET house_id = NULL, house_name = '' WHERE parking_id = ?", parkingId);
        } catch (Exception e) {
            logger.error("清空车位房屋信息失败", e);
        }
    }

    // ==================== 房屋-车辆绑定管理 ====================

    /**
     * 获取可绑定的车辆列表
     */
    @PostMapping("/house-vehicle/available")
    @ResponseBody
    public AjaxResult getAvailableVehicles() {
        JSONObject params = getParams();
        String search = params.getString("search");
        String houseId = params.getString("houseId");
        int page = params.containsKey("page") ? params.getIntValue("page") : 1;
        int pageSize = 20;

        try {
            String sql = "SELECT vehicle_id as id, plate_no as text " +
                        "FROM eh_vehicle " +
                        "WHERE community_id = ? " +
                        "AND vehicle_id NOT IN (SELECT vehicle_id FROM eh_vehicle_house_rel WHERE house_id = ?) ";

            List<Object> sqlParams = new ArrayList<>();
            sqlParams.add(getSysUser().getCommunityId());
            sqlParams.add(houseId);

            if (StringUtils.isNotEmpty(search)) {
                sql += "AND plate_no LIKE ? ";
                sqlParams.add("%" + search + "%");
            }

            sql += "ORDER BY plate_no LIMIT ?, ?";
            sqlParams.add((page - 1) * pageSize);
            sqlParams.add(pageSize);

            List<Record> list = Db.find(sql, sqlParams.toArray());

            Map<String, Object> result = new HashMap<>();
            result.put("data", recordToMap(list));
            result.put("total", list.size());

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取可绑定车辆列表失败", e);
            return AjaxResult.error("获取数据失败：" + e.getMessage());
        }
    }

    /**
     * 添加车辆绑定
     */
    @PostMapping("/house-vehicle/add")
    @ResponseBody
    @Log(title = "绑定车辆", businessType = BusinessType.INSERT)
    public AjaxResult addVehicleBinding() {
        JSONObject params = getParams();
        String houseId = params.getString("houseId");
        String vehicleId = params.getString("vehicleId");

        if (StringUtils.isEmpty(houseId)) {
            return AjaxResult.error("房屋ID不能为空");
        }
        if (StringUtils.isEmpty(vehicleId)) {
            return AjaxResult.error("车辆ID不能为空");
        }

        try {
            // 检查是否已绑定
            Record existRel = Db.findFirst(
                "SELECT * FROM eh_vehicle_house_rel WHERE house_id = ? AND vehicle_id = ?",
                houseId, vehicleId
            );

            if (existRel != null) {
                return AjaxResult.error("该车辆已绑定到此房屋");
            }

            // 创建绑定关系
            Record rel = new Record();
            rel.set("rel_id", Seq.getId());
            rel.set("house_id", houseId);
            rel.set("vehicle_id", vehicleId);
            rel.set("is_default", 0);
            rel.set("check_status", "1"); // 默认已审核
            setCreateAndUpdateInfo(rel);

            boolean success = Db.save("eh_vehicle_house_rel", "rel_id", rel);
            return toAjax(success);
        } catch (Exception e) {
            logger.error("绑定车辆失败", e);
            return AjaxResult.error("绑定失败：" + e.getMessage());
        }
    }

    /**
     * 解除车辆绑定
     */
    @PostMapping("/house-vehicle/remove")
    @ResponseBody
    @Log(title = "解绑车辆", businessType = BusinessType.DELETE)
    public AjaxResult removeVehicleBinding() {
        JSONObject params = getParams();
        String relId = params.getString("relId");
        if (StringUtils.isEmpty(relId)) {
            return AjaxResult.error("关系ID不能为空");
        }

        try {
            Record rel = Db.findById("eh_vehicle_house_rel", "rel_id", relId);
            if (rel == null) {
                return AjaxResult.error("绑定关系不存在");
            }

            boolean success = Db.deleteById("eh_vehicle_house_rel", "rel_id", relId);
            return toAjax(success);
        } catch (Exception e) {
            logger.error("解绑车辆失败", e);
            return AjaxResult.error("解绑失败：" + e.getMessage());
        }
    }

    /**
     * 查询房屋绑定的车辆列表
     */
    @RequestMapping("/house-vehicle/list")
    @ResponseBody
    public TableDataInfo vehicleList() {
        JSONObject params = getParams();
        String houseId = params.getString("houseId");
        if (StringUtils.isEmpty(houseId)) {
            return getDataTable(new Page<>());
        }
        // 查询车辆绑定关系及车辆信息（不再排序is_default）
        String sql = "SELECT r.rel_id, r.house_id, r.vehicle_id, r.check_status, r.remark, r.create_by, r.create_time, r.update_by, r.update_time, " +
                     "v.plate_no, v.vehicle_type, v.vehicle_brand, v.vehicle_model, v.owner_real_name, v.parking_space, v.remark as vehicle_remark " +
                     "FROM eh_vehicle_house_rel r " +
                     "LEFT JOIN eh_vehicle v ON r.vehicle_id = v.vehicle_id " +
                     "WHERE r.house_id = ? " +
                     "ORDER BY r.create_time DESC";
        List<Record> list = Db.find(sql, houseId);
        return getDataList(list);
    }

    private void setCreateAndUpdateInfo(Record record) {
        String now = DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS);
        String loginName = getSysUser().getLoginName();
        record.set("community_id", getSysUser().getCommunityId());
        record.set("create_time", now);
        record.set("update_time", now);
        record.set("create_by", loginName);
        record.set("update_by", loginName);
    }

    // ==================== 业主-车位绑定管理 ====================

    /**
     * 获取可选车位列表（从业主角度）
     */
    @PostMapping("/owner-parking/available")
    @ResponseBody
    public AjaxResult getAvailableParkingsForOwner() {
        JSONObject params = getParams();
        String search = params.getString("search");
        String ownerId = params.getString("ownerId");
        Integer page = params.getInteger("page");

        if (page == null) page = 1;
        int pageSize = 20; // 每页20条数据

        try {
            EasySQL sql = new EasySQL();
            sql.append("FROM eh_parking_space WHERE 1=1");
            sql.append(getSysUser().getCommunityId(), "AND community_id = ?");
            sql.append("AND check_status = 1"); // 只显示已审核的车位

            // 排除已绑定给该业主的车位
            if (StringUtils.isNotEmpty(ownerId)) {
                sql.append("AND parking_id NOT IN (SELECT parking_id FROM eh_parking_owner_rel WHERE owner_id = '" + ownerId + "')");
            }

            // 搜索条件 - 如果有搜索词则添加搜索条件
            if (StringUtils.isNotEmpty(search)) {
                sql.append("AND parking_no LIKE '%" + search + "%'");
            }

            sql.append("ORDER BY parking_no");

            // 使用分页查询
            Page<Record> pageResult = Db.paginate(page, pageSize,
                "SELECT parking_id as id, CONCAT(parking_no, ' (', CASE parking_type WHEN 1 THEN '私人车位' WHEN 2 THEN '子母车位' ELSE '未知' END, ')') as text",
                sql.toFullSql());

            return AjaxResult.success(recordToMap(pageResult.getList()));
        } catch (Exception e) {
            logger.error("获取可选车位列表失败", e);
            return AjaxResult.error("获取车位列表失败");
        }
    }

    /**
     * 添加车位绑定（从业主角度）
     */
    @PostMapping("/owner-parking/add")
    @ResponseBody
    @Log(title = "绑定车位", businessType = BusinessType.INSERT)
    public AjaxResult addParkingBindingForOwner() {
        JSONObject params = getParams();
        String ownerId = params.getString("ownerId");
        String parkingId = params.getString("parkingId");

        if (StringUtils.isEmpty(ownerId)) {
            return AjaxResult.error("业主ID不能为空");
        }
        if (StringUtils.isEmpty(parkingId)) {
            return AjaxResult.error("车位ID不能为空");
        }

        try {
            // 检查是否已绑定
            Record existRel = Db.findFirst(
                "SELECT * FROM eh_parking_owner_rel WHERE parking_id = ? AND owner_id = ?",
                parkingId, ownerId
            );

            if (existRel != null) {
                return AjaxResult.error("该业主已绑定到此车位");
            }

            // 创建绑定关系
            Record rel = new Record();
            rel.set("rel_id", Seq.getId());
            rel.set("parking_id", parkingId);
            rel.set("owner_id", ownerId);
            rel.set("rel_type", 1); // 默认为业主
            rel.set("is_default", 0);
            rel.set("check_status", 1); // 默认已审核
            rel.set("create_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
            rel.set("create_by", getSysUser().getLoginName());

            boolean success = Db.save("eh_parking_owner_rel", "rel_id", rel);
            if (success) {
                // 更新车位的绑定业主数量和业主名称
                updateParkingOwnerInfo(parkingId);
                // 更新业主的车位统计信息
                updateOwnerParkingInfo(ownerId);
            }

            return toAjax(success);
        } catch (Exception e) {
            logger.error("绑定车位失败", e);
            return AjaxResult.error("绑定失败：" + e.getMessage());
        }
    }

    /**
     * 移除车位绑定（从业主角度）
     */
    @PostMapping("/owner-parking/remove")
    @ResponseBody
    @Log(title = "解绑车位", businessType = BusinessType.DELETE)
    public AjaxResult removeParkingBindingForOwner() {
        JSONObject params = getParams();
        String relId = params.getString("relId");

        if (StringUtils.isEmpty(relId)) {
            return AjaxResult.error("关系ID不能为空");
        }

        try {
            // 获取绑定关系信息
            Record rel = Db.findFirst("SELECT * FROM eh_parking_owner_rel WHERE rel_id = ?", relId);
            if (rel == null) {
                return AjaxResult.error("绑定关系不存在");
            }

            String parkingId = rel.getStr("parking_id");

            // 删除绑定关系
            boolean success = Db.deleteById("eh_parking_owner_rel", "rel_id", relId);
            if (success) {
                // 更新车位的绑定业主数量和业主名称
                updateParkingOwnerInfo(parkingId);
                // 更新业主的车位统计信息
                updateOwnerParkingInfo(rel.getStr("owner_id"));
            }

            return toAjax(success);
        } catch (Exception e) {
            logger.error("解绑车位失败", e);
            return AjaxResult.error("解绑失败：" + e.getMessage());
        }
    }

    // ==================== 业主-车辆绑定管理 ====================

    /**
     * 获取可选车辆列表（从业主角度）
     */
    @PostMapping("/owner-vehicle/available")
    @ResponseBody
    public AjaxResult getAvailableVehiclesForOwner() {
        JSONObject params = getParams();
        String search = params.getString("search");
        String ownerId = params.getString("ownerId");
        Integer page = params.getInteger("page");

        if (page == null) page = 1;
        int pageSize = 20; // 每页20条数据

        try {
            EasySQL sql = new EasySQL();
            sql.append("FROM eh_vehicle WHERE 1=1");
            sql.append(getSysUser().getCommunityId(), "AND community_id = ?");
            sql.append("AND check_status = 1"); // 只显示已审核的车辆

            // 排除已绑定给该业主的车辆
            if (StringUtils.isNotEmpty(ownerId)) {
                sql.append("AND vehicle_id NOT IN (SELECT vehicle_id FROM eh_vehicle_owner_rel WHERE owner_id = '" + ownerId + "')");
            }

            // 搜索条件 - 如果有搜索词则添加搜索条件
            if (StringUtils.isNotEmpty(search)) {
                sql.append("AND (plate_no LIKE '%" + search + "%' OR vehicle_brand LIKE '%" + search + "%')");
            }

            sql.append("ORDER BY plate_no");

            // 使用分页查询
            Page<Record> pageResult = Db.paginate(page, pageSize,
                "SELECT vehicle_id as id, CONCAT(plate_no, CASE WHEN vehicle_brand IS NOT NULL AND vehicle_brand != '' THEN CONCAT(' (', vehicle_brand, CASE WHEN vehicle_model IS NOT NULL AND vehicle_model != '' THEN CONCAT(' ', vehicle_model) ELSE '' END, ')') ELSE '' END) as text",
                sql.toFullSql());

            return AjaxResult.success(recordToMap(pageResult.getList()));
        } catch (Exception e) {
            logger.error("获取可选车辆列表失败", e);
            return AjaxResult.error("获取车辆列表失败");
        }
    }

    /**
     * 添加车辆绑定（从业主角度）
     */
    @PostMapping("/owner-vehicle/add")
    @ResponseBody
    @Log(title = "绑定车辆", businessType = BusinessType.INSERT)
    public AjaxResult addVehicleBindingForOwner() {
        JSONObject params = getParams();
        String ownerId = params.getString("ownerId");
        String vehicleId = params.getString("vehicleId");

        if (StringUtils.isEmpty(ownerId)) {
            return AjaxResult.error("业主ID不能为空");
        }
        if (StringUtils.isEmpty(vehicleId)) {
            return AjaxResult.error("车辆ID不能为空");
        }

        try {
            // 检查是否已绑定
            Record existRel = Db.findFirst(
                "SELECT * FROM eh_vehicle_owner_rel WHERE vehicle_id = ? AND owner_id = ?",
                vehicleId, ownerId
            );

            if (existRel != null) {
                return AjaxResult.error("该业主已绑定到此车辆");
            }

            // 创建绑定关系
            Record rel = new Record();
            rel.set("rel_id", Seq.getId());
            rel.set("vehicle_id", vehicleId);
            rel.set("owner_id", ownerId);
            rel.set("is_default", 0);
            rel.set("check_status", "1"); // 默认已审核
            rel.set("create_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
            rel.set("create_by", getSysUser().getLoginName());

            boolean success = Db.save("eh_vehicle_owner_rel", "rel_id", rel);
            if (success) {
                // 更新车辆的绑定业主数量
                Db.update(
                    "UPDATE eh_vehicle SET owner_count = (SELECT COUNT(*) FROM eh_vehicle_owner_rel WHERE vehicle_id = ?) WHERE vehicle_id = ?",
                    vehicleId, vehicleId
                );

                // 更新业主的车辆统计信息
                updateOwnerVehicleInfo(ownerId);
            }

            return toAjax(success);
        } catch (Exception e) {
            logger.error("绑定车辆失败", e);
            return AjaxResult.error("绑定失败：" + e.getMessage());
        }
    }

    /**
     * 移除车辆绑定（从业主角度）
     */
    @PostMapping("/owner-vehicle/remove")
    @ResponseBody
    @Log(title = "解绑车辆", businessType = BusinessType.DELETE)
    public AjaxResult removeVehicleBindingForOwner() {
        JSONObject params = getParams();
        String relId = params.getString("relId");

        if (StringUtils.isEmpty(relId)) {
            return AjaxResult.error("关系ID不能为空");
        }

        try {
            // 获取绑定关系信息
            Record rel = Db.findFirst("SELECT * FROM eh_vehicle_owner_rel WHERE rel_id = ?", relId);
            if (rel == null) {
                return AjaxResult.error("绑定关系不存在");
            }

            String vehicleId = rel.getStr("vehicle_id");

            // 删除绑定关系
            boolean success = Db.deleteById("eh_vehicle_owner_rel", "rel_id", relId);
            if (success) {
                // 更新车辆的绑定业主数量
                Db.update("UPDATE eh_vehicle SET owner_count = (SELECT COUNT(*) FROM eh_vehicle_owner_rel WHERE vehicle_id = ?) WHERE vehicle_id = ?",
                    vehicleId, vehicleId
                );

                // 更新业主的车辆统计信息
                updateOwnerVehicleInfo(rel.getStr("owner_id"));
            }

            return toAjax(success);
        } catch (Exception e) {
            logger.error("解绑车辆失败", e);
            return AjaxResult.error("解绑失败：" + e.getMessage());
        }
    }

    /**
     * 设置默认住户
     */
     @Log(title = "设置默认住户", businessType = BusinessType.UPDATE)
     @PostMapping("/house-owner/setDefault")
     @ResponseBody
     public AjaxResult setDefaultOwner() {
         JSONObject params = getParams();
         String relId = params.getString("relId");
         Long houseId = params.getLong("houseId");
         String ownerId = params.getString("ownerId");

         if (StringUtils.isEmpty(relId)) {
             return AjaxResult.error("关系ID不能为空");
         }
         if (StringUtils.isEmpty(ownerId)) {
             return AjaxResult.error("ownerId不能为空");
         }

         if (houseId==null||houseId==0) {
             return AjaxResult.error("房屋ID不能为空");
         }
         int resultRow = houseInfoService.setDefaultHouse(houseId, ownerId);
         return toAjax(resultRow>0);
     }

     /**
      * 审核住户
      */
     @Log(title = "审核住户", businessType = BusinessType.UPDATE)
     @PostMapping("/house-owner/approve")
     @ResponseBody
     public AjaxResult approveOwner() {
         JSONObject params = getParams();
         String relId = params.getString("relId");
         String checkStatus = params.getString("checkStatus");

         if (StringUtils.isEmpty(relId)) {
             return AjaxResult.error("关系ID不能为空");
         }
         if (StringUtils.isEmpty(checkStatus)) {
             return AjaxResult.error("审核状态不能为空");
         }

         try {
             // 查询关系信息
             Record rel = Db.findById("eh_house_owner_rel", "rel_id", relId);
             if (rel == null) {
                 return AjaxResult.error("绑定关系不存在");
             }

             // 更新审核状态
             rel.set("check_status", checkStatus);
             setUpdateInfo(rel);

             boolean success = Db.update("eh_house_owner_rel", "rel_id", rel);
             return toAjax(success);
         } catch (Exception e) {
             logger.error("审核住户失败", e);
             return AjaxResult.error("审核失败：" + e.getMessage());
         }
     }

    // ==================== 辅助方法 ====================

    /**
     * 更新车位的业主信息
     */
    private void updateParkingOwnerInfo(String parkingId) {
        try {
            // 更新车位的绑定业主数量
            Db.update("UPDATE eh_parking_space SET owner_count = (SELECT COUNT(*) FROM eh_parking_owner_rel WHERE parking_id = ?) WHERE parking_id = ?",
                parkingId, parkingId);
        } catch (Exception e) {
            logger.error("更新车位业主信息失败", e);
        }
    }

    /**
     * 更新业主的车位统计信息
     */
    private void updateOwnerParkingInfo(String ownerId) {
        try {
            // 更新业主的车位数量
            Db.update("UPDATE eh_owner SET parking_count = (SELECT COUNT(*) FROM eh_parking_owner_rel WHERE owner_id = ?) WHERE owner_id = ?",
                ownerId, ownerId);
        } catch (Exception e) {
            logger.error("更新业主车位信息失败", e);
        }
    }

    /**
     * 更新业主的车辆统计信息
     */
    private void updateOwnerVehicleInfo(String ownerId) {
        try {
            // 更新业主的车辆数量
            Db.update("UPDATE eh_owner SET car_count = (SELECT COUNT(*) FROM eh_vehicle_owner_rel WHERE owner_id = ?) WHERE owner_id = ?",
                ownerId, ownerId);
        } catch (Exception e) {
            logger.error("更新业主车辆信息失败", e);
        }
    }

    private void setUpdateInfo(Record record) {
        record.set("update_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
        record.set("update_by", getSysUser().getLoginName());
    }
}
